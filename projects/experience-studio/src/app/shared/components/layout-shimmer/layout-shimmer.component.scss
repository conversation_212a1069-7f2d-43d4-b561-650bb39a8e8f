// Base layout structure
.layout-shimmer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  
  // Light theme
  &.theme-light {
    background: #f8f9fa;
    
    .shimmer-element {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
    }
    
    .header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .sidebar {
      background: #f9fafb;
      border-color: #e5e7eb;
    }
    
    .body {
      background: white;
    }
    
    .footer {
      background: #111827;
      
      .shimmer-element {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
      }
    }
  }
  
  // Dark theme
  &.theme-dark {
    background: #1a1a1a;
    
    .shimmer-element {
      background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
      background-size: 200% 100%;
    }
    
    .header {
      background: #2d2d2d;
      border-bottom: 1px solid #404040;
    }
    
    .sidebar {
      background: #252525;
      border-color: #404040;
    }
    
    .body {
      background: #2d2d2d;
    }
    
    .footer {
      background: #0f0f0f;
      
      .shimmer-element {
        background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
        background-size: 200% 100%;
      }
    }
  }
  
  // Animation
  &.animated .shimmer-element {
    animation: shimmer 1.5s infinite;
  }
}

// Shimmer animation
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

// Base shimmer element
.shimmer-element {
  border-radius: 6px;
  
  // Sizes
  &.logo { width: 128px; height: 32px; }
  &.nav-item { width: 80px; height: 16px; }
  &.avatar { width: 32px; height: 32px; border-radius: 50%; }
  &.button { width: 80px; height: 32px; }
  
  &.sidebar-title { width: 128px; height: 24px; margin-bottom: 8px; }
  &.sidebar-item { width: 96px; height: 16px; margin-bottom: 8px; }
  
  &.page-title { width: 50%; height: 32px; margin-bottom: 24px; }
  &.card-image { width: 100%; height: 128px; margin-bottom: 16px; }
  &.card-title { width: 75%; height: 16px; margin-bottom: 8px; }
  &.card-text { width: 50%; height: 16px; }
  
  &.text-line {
    height: 16px;
    margin-bottom: 8px;
    
    &.full { width: 100%; }
    &.three-quarter { width: 75%; }
    &.half { width: 50%; }
  }
  
  &.footer-title { width: 96px; height: 24px; margin-bottom: 16px; }
  &.footer-link { width: 80px; height: 16px; margin-bottom: 8px; }
}

// Header
.header {
  padding: 16px 24px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// Main container
.main-container {
  flex: 1;
  display: flex;
}

// Sidebar
.sidebar {
  width: 250px;
  padding: 24px;
  
  &.left-sidebar {
    border-right: 1px solid;
  }
  
  &.right-sidebar {
    border-left: 1px solid;
  }
  
  .sidebar-section {
    margin-bottom: 24px;
  }
}

// Body
.body {
  flex: 1;
  padding: 24px;
  
  .content-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .content-card {
    padding: 16px;
  }
  
  .text-content {
    margin-top: 24px;
  }
}

// Footer
.footer {
  padding: 32px 24px;
  
  .footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
  
  .body .content-grid {
    grid-template-columns: 1fr;
  }
  
  .footer .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .footer .footer-content {
    grid-template-columns: 1fr;
  }
}
