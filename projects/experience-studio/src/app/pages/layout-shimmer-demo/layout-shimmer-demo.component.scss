.demo-container {
  min-height: 100vh;
  display: flex;

  // Hardware acceleration for smooth split screen performance
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;

  &.theme-light {
    background: #f8f9fa;
    color: #333;

    .controls-panel {
      background: white;
      border-right: 1px solid #e5e7eb;
    }

    .layout-btn {
      background: white;
      border: 1px solid #d1d5db;
      color: #374151;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #dbeafe;
        border-color: #3b82f6;
        color: #1e40af;
      }
    }

    .toggle-btn {
      background: white;
      border: 1px solid #d1d5db;
      color: #374151;

      &:hover {
        background: #f9fafb;
      }
    }

    .preview-container {
      background: white;
    }

    .info-badge {
      background: #f3f4f6;
      color: #374151;

      &.theme-badge {
        background: #dbeafe;
        color: #1e40af;
      }

      &.animation-badge.active {
        background: #dcfce7;
        color: #166534;
      }
    }
  }

  &.theme-dark {
    background: #1a1a1a;
    color: #e5e7eb;

    .controls-panel {
      background: #2d2d2d;
      border-right: 1px solid #404040;
    }

    .layout-btn {
      background: #374151;
      border: 1px solid #4b5563;
      color: #e5e7eb;

      &:hover {
        background: #4b5563;
      }

      &.active {
        background: #1e40af;
        border-color: #3b82f6;
        color: white;
      }
    }

    .toggle-btn {
      background: #374151;
      border: 1px solid #4b5563;
      color: #e5e7eb;

      &:hover {
        background: #4b5563;
      }
    }

    .preview-container {
      background: #2d2d2d;
    }

    .info-badge {
      background: #374151;
      color: #e5e7eb;

      &.theme-badge {
        background: #1e40af;
        color: white;
      }

      &.animation-badge.active {
        background: #166534;
        color: #dcfce7;
      }
    }
  }
}

.controls-panel {
  width: 350px;
  padding: 24px;
  overflow-y: auto;

  .controls-header {
    margin-bottom: 32px;

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      opacity: 0.7;
      font-size: 14px;
    }
  }

  .controls-section {
    margin-bottom: 32px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }

  .layout-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }

  .layout-btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .layout-description {
    font-size: 14px;
    opacity: 0.8;
    font-style: italic;
  }

  .toggle-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .toggle-btn {
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    .toggle-icon {
      font-size: 16px;
    }
  }
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;

  .preview-header {
    padding: 24px;
    border-bottom: 1px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      font-size: 18px;
      font-weight: 600;
    }

    .preview-info {
      display: flex;
      gap: 8px;
    }

    .info-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .preview-frame {
    flex: 1;
    overflow: hidden;
    position: relative;

    // Hardware acceleration for smooth resizing
    transform: translateZ(0);
    will-change: transform, width, height;

    app-layout-shimmer {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      display: block;

      // Container queries for responsive shimmer
      container-type: size;
      container-name: demo-shimmer;
    }
  }
}

// Split screen responsive design
@media (max-width: 1200px) {
  .demo-container {
    .controls-panel {
      width: 300px;
    }
  }
}

@media (max-width: 900px) {
  .demo-container {
    .controls-panel {
      width: 250px;

      .layout-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
      }

      .layout-btn {
        padding: 6px 8px;
        font-size: 11px;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-container {
    flex-direction: column;
  }

  .controls-panel {
    width: 100%;
    max-height: 300px;
  }

  .layout-buttons {
    grid-template-columns: repeat(4, 1fr);
  }

  .toggle-controls {
    flex-direction: row;
  }
}

@media (max-width: 600px) {
  .demo-container {
    .controls-panel {
      padding: 16px;

      .controls-header h1 {
        font-size: 20px;
      }

      .layout-buttons {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
      }

      .layout-btn {
        padding: 4px 6px;
        font-size: 10px;
      }
    }
  }
}
