import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'experience',
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
        data: { preload: true }
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
      },
      // New routes for Generate Application
      {
        path: 'generate-application',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            data: { cardType: 'Generate Application' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            data: { cardType: 'Generate Application' }
          }
        ]
      },
      // New routes for Generate UI Design
      {
        path: 'generate-ui-design',
        children: [
          {
            path: 'prompt',
            loadComponent: () =>
              import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
                m => m.PromptContentComponent
              ),
            data: { cardType: 'Generate UI Design' }
          },
          {
            path: 'code-preview',
            loadComponent: () =>
              import('./shared/components/code-window/code-window.component').then(
                m => m.CodeWindowComponent
              ),
            data: { cardType: 'Generate UI Design' }
          }
        ]
      }
    ]
  },
  {
    path: '',
    redirectTo: 'experience',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'experience',
  },
];
