// Base layout structure
.layout-shimmer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;

  // Light theme
  &.theme-light {
    background: #f8f9fa;

    .shimmer-element {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
    }

    .header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }

    .sidebar {
      background: #f9fafb;
      border-color: #e5e7eb;
    }

    .body {
      background: white;
    }

    .footer {
      background: #111827;

      .shimmer-element {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Dark theme
  &.theme-dark {
    background: #1a1a1a;

    .shimmer-element {
      background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
      background-size: 200% 100%;
    }

    .header {
      background: #2d2d2d;
      border-bottom: 1px solid #404040;
    }

    .sidebar {
      background: #252525;
      border-color: #404040;
    }

    .body {
      background: #2d2d2d;
    }

    .footer {
      background: #0f0f0f;

      .shimmer-element {
        background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Animation
  &.animated .shimmer-element {
    animation: shimmer 1.5s infinite;
  }
}

// Shimmer animation
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

// Base shimmer element
.shimmer-element {
  border-radius: 4px;

  // Sizes
  &.logo { width: min(120px, 30%); height: 24px; }
  &.nav-item { width: min(60px, 20%); height: 12px; }
  &.avatar { width: 24px; height: 24px; border-radius: 50%; }
  &.button { width: min(70px, 25%); height: 24px; }

  &.sidebar-title { width: 80%; height: 18px; margin-bottom: 6px; }
  &.sidebar-item { width: 70%; height: 12px; margin-bottom: 6px; }

  &.page-title { width: 40%; height: 24px; margin-bottom: 16px; }
  &.card-image { width: 100%; height: min(80px, 15vh); margin-bottom: 12px; }
  &.card-title { width: 75%; height: 12px; margin-bottom: 6px; }
  &.card-text { width: 50%; height: 12px; }

  &.text-line {
    height: 12px;
    margin-bottom: 6px;

    &.full { width: 100%; }
    &.three-quarter { width: 75%; }
    &.half { width: 50%; }
  }

  &.footer-title { width: 80%; height: 18px; margin-bottom: 12px; }
  &.footer-link { width: 70%; height: 12px; margin-bottom: 6px; }
}

// Header
.header {
  padding: 12px 16px;
  flex-shrink: 0;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// Main container
.main-container {
  flex: 1;
  display: flex;
}

// Sidebar
.sidebar {
  width: 200px;
  padding: 16px;
  flex-shrink: 0;

  &.left-sidebar {
    border-right: 1px solid;
  }

  &.right-sidebar {
    border-left: 1px solid;
  }

  .sidebar-section {
    margin-bottom: 16px;
  }
}

// Body
.body {
  flex: 1;
  padding: 16px;
  overflow: hidden;

  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
  }

  .content-card {
    padding: 8px;
  }

  .text-content {
    margin-top: 16px;
  }
}

// Footer
.footer {
  padding: 16px;
  flex-shrink: 0;

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .sidebar {
    width: 150px;
    padding: 12px;
  }

  .header {
    padding: 8px 12px;
  }

  .body {
    padding: 12px;

    .content-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 8px;
    }
  }

  .footer {
    padding: 12px;

    .footer-content {
      grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
      gap: 12px;
    }
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 120px;
    padding: 8px;
  }

  .header {
    padding: 6px 8px;

    .header-left,
    .header-right {
      gap: 8px;
    }
  }

  .body {
    padding: 8px;

    .content-grid {
      grid-template-columns: 1fr;
      gap: 6px;
    }
  }

  .footer .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

@media (max-width: 320px) {
  .sidebar {
    width: 100px;
    padding: 6px;
  }

  .footer .footer-content {
    grid-template-columns: 1fr;
  }
}
