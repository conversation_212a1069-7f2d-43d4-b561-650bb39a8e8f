// Base layout structure
.layout-shimmer {
  width: 100%;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow: hidden;

  // Hardware acceleration for smooth split screen performance
  transform: translateZ(0);
  will-change: transform, width, height;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // Light theme
  &.theme-light {
    background: #f8f9fa;

    .shimmer-element {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
    }

    .header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }

    .sidebar {
      background: #f9fafb;
      border-color: #e5e7eb;
    }

    .body {
      background: white;
    }

    .footer {
      background: #111827;

      .shimmer-element {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Dark theme
  &.theme-dark {
    background: #1a1a1a;

    .shimmer-element {
      background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
      background-size: 200% 100%;
    }

    .header {
      background: #2d2d2d;
      border-bottom: 1px solid #404040;
    }

    .sidebar {
      background: #252525;
      border-color: #404040;
    }

    .body {
      background: #2d2d2d;
    }

    .footer {
      background: #0f0f0f;

      .shimmer-element {
        background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
        background-size: 200% 100%;
      }
    }
  }

  // Animation - slower, smoother, and more fluid
  &.animated .shimmer-element {
    animation: shimmer 3s ease-in-out infinite;
    animation-fill-mode: both;
  }
}

// Shimmer animation - slower and more fluid
@keyframes shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0.6;
  }
  25% {
    opacity: 0.8;
  }
  50% {
    background-position: 0% 0;
    opacity: 1;
  }
  75% {
    opacity: 0.8;
  }
  100% {
    background-position: 200% 0;
    opacity: 0.6;
  }
}

// Base shimmer element
.shimmer-element {
  border-radius: 6px;

  // Sizes
  &.logo { width: 128px; height: 32px; }
  &.nav-item { width: 80px; height: 16px; }
  &.avatar { width: 32px; height: 32px; border-radius: 50%; }
  &.button { width: 80px; height: 32px; }

  &.sidebar-title { width: 128px; height: 24px; margin-bottom: 8px; }
  &.sidebar-item { width: 96px; height: 16px; margin-bottom: 8px; }

  &.page-title { width: 50%; height: 32px; margin-bottom: 24px; }
  &.card-image { width: 100%; height: 128px; margin-bottom: 16px; }
  &.card-title { width: 75%; height: 16px; margin-bottom: 8px; }
  &.card-text { width: 50%; height: 16px; }

  &.text-line {
    height: 16px;
    margin-bottom: 8px;

    &.full { width: 100%; }
    &.three-quarter { width: 75%; }
    &.half { width: 50%; }
  }

  &.footer-title { width: 96px; height: 24px; margin-bottom: 16px; }
  &.footer-link { width: 80px; height: 16px; margin-bottom: 8px; }
}

// Header
.header {
  padding: 16px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

// Main container
.main-container {
  flex: 1;
  display: flex;
}

// Sidebar
.sidebar {
  width: 250px;
  padding: 24px;

  &.left-sidebar {
    border-right: 1px solid;
  }

  &.right-sidebar {
    border-left: 1px solid;
  }

  .sidebar-section {
    margin-bottom: 24px;
  }
}

// Body
.body {
  flex: 1;
  padding: 24px;

  .content-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }

  .content-card {
    padding: 16px;
  }

  .text-content {
    margin-top: 24px;
  }
}

// Footer
.footer {
  padding: 32px 24px;

  .footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

// Split screen responsive design - container-based queries
.layout-shimmer {
  // Very narrow containers (split screen with small panel)
  &:has(.main-container) {
    @container (max-width: 400px) {
      .sidebar {
        width: 150px;
        padding: 12px;
      }

      .body {
        padding: 12px;

        .content-grid {
          grid-template-columns: 1fr;
          gap: 8px;
        }
      }

      .shimmer-element {
        &.logo { width: 60px; height: 18px; }
        &.nav-item { width: 40px; height: 10px; }
        &.avatar { width: 18px; height: 18px; }
        &.button { width: 40px; height: 18px; }
        &.sidebar-title { width: 60px; height: 14px; }
        &.sidebar-item { width: 50px; height: 10px; }
        &.page-title { height: 18px; margin-bottom: 12px; }
        &.card-image { height: 60px; margin-bottom: 8px; }
        &.card-title { height: 10px; margin-bottom: 4px; }
        &.card-text { height: 10px; }
        &.text-line { height: 10px; margin-bottom: 4px; }
        &.footer-title { width: 50px; height: 14px; }
        &.footer-link { width: 40px; height: 10px; }
      }
    }
  }
}

// Traditional responsive design for screen width
@media (max-width: 768px) {
  .layout-shimmer {
    .sidebar {
      display: none;
    }

    .body .content-grid {
      grid-template-columns: 1fr;
    }

    .footer .footer-content {
      grid-template-columns: repeat(2, 1fr);
    }

    // Scale down shimmer elements for smaller screens
    .shimmer-element {
      &.logo { width: 96px; height: 24px; }
      &.nav-item { width: 60px; height: 12px; }
      &.avatar { width: 24px; height: 24px; }
      &.button { width: 60px; height: 24px; }
      &.sidebar-title { width: 96px; height: 18px; }
      &.sidebar-item { width: 72px; height: 12px; }
      &.page-title { height: 24px; }
      &.card-image { height: 96px; }
      &.card-title { height: 12px; }
      &.card-text { height: 12px; }
      &.text-line { height: 12px; }
      &.footer-title { width: 72px; height: 18px; }
      &.footer-link { width: 60px; height: 12px; }
    }
  }
}

@media (max-width: 480px) {
  .layout-shimmer {
    .footer .footer-content {
      grid-template-columns: 1fr;
    }

    // Further scale down for very small screens
    .shimmer-element {
      &.logo { width: 80px; height: 20px; }
      &.nav-item { width: 48px; height: 10px; }
      &.avatar { width: 20px; height: 20px; }
      &.button { width: 48px; height: 20px; }
      &.page-title { height: 20px; }
      &.card-image { height: 80px; }
    }
  }
}

// Container responsive scaling
@media (max-height: 600px) {
  .layout-shimmer {
    .header { padding: 12px 16px; }
    .sidebar { padding: 16px; }
    .body { padding: 16px; }
    .footer { padding: 20px 16px; }

    .shimmer-element {
      &.card-image { height: 80px; }
      &.page-title { margin-bottom: 16px; }
    }
  }
}

@media (max-height: 400px) {
  .layout-shimmer {
    .header { padding: 8px 12px; }
    .sidebar { padding: 12px; }
    .body { padding: 12px; }
    .footer { padding: 16px 12px; }

    .shimmer-element {
      &.card-image { height: 60px; }
      &.page-title { margin-bottom: 12px; }
    }
  }
}
