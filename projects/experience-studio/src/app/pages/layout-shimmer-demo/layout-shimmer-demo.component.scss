.demo-container {
  min-height: 100vh;
  display: flex;
  overflow: hidden;

  &.theme-light {
    background: #f8f9fa;
    color: #333;

    .controls-panel {
      background: white;
      border-right: 1px solid #e5e7eb;
    }

    .layout-btn {
      background: white;
      border: 1px solid #d1d5db;
      color: #374151;

      &:hover {
        background: #f9fafb;
      }

      &.active {
        background: #dbeafe;
        border-color: #3b82f6;
        color: #1e40af;
      }
    }

    .toggle-btn {
      background: white;
      border: 1px solid #d1d5db;
      color: #374151;

      &:hover {
        background: #f9fafb;
      }
    }

    .preview-container {
      background: white;
    }

    .info-badge {
      background: #f3f4f6;
      color: #374151;

      &.theme-badge {
        background: #dbeafe;
        color: #1e40af;
      }

      &.animation-badge.active {
        background: #dcfce7;
        color: #166534;
      }
    }
  }

  &.theme-dark {
    background: #1a1a1a;
    color: #e5e7eb;

    .controls-panel {
      background: #2d2d2d;
      border-right: 1px solid #404040;
    }

    .layout-btn {
      background: #374151;
      border: 1px solid #4b5563;
      color: #e5e7eb;

      &:hover {
        background: #4b5563;
      }

      &.active {
        background: #1e40af;
        border-color: #3b82f6;
        color: white;
      }
    }

    .toggle-btn {
      background: #374151;
      border: 1px solid #4b5563;
      color: #e5e7eb;

      &:hover {
        background: #4b5563;
      }
    }

    .preview-container {
      background: #2d2d2d;
    }

    .info-badge {
      background: #374151;
      color: #e5e7eb;

      &.theme-badge {
        background: #1e40af;
        color: white;
      }

      &.animation-badge.active {
        background: #166534;
        color: #dcfce7;
      }
    }
  }
}

.controls-panel {
  width: 300px;
  min-width: 250px;
  padding: 16px;
  overflow-y: auto;
  flex-shrink: 0;

  .controls-header {
    margin-bottom: 32px;

    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      opacity: 0.7;
      font-size: 14px;
    }
  }

  .controls-section {
    margin-bottom: 32px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }
  }

  .layout-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }

  .layout-btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .layout-description {
    font-size: 14px;
    opacity: 0.8;
    font-style: italic;
  }

  .toggle-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .toggle-btn {
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    .toggle-icon {
      font-size: 16px;
    }

    &:hover {
      transform: translateY(-1px);
    }
  }
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .preview-header {
    padding: 16px;
    border-bottom: 1px solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    h3 {
      font-size: 18px;
      font-weight: 600;
    }

    .preview-info {
      display: flex;
      gap: 8px;
    }

    .info-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .preview-frame {
    flex: 1;
    overflow: hidden;
    position: relative;

    app-layout-shimmer {
      width: 100%;
      height: 100%;
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .controls-panel {
    width: 250px;
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .demo-container {
    flex-direction: column;
  }

  .controls-panel {
    width: 100%;
    max-height: 250px;
    padding: 12px;
  }

  .layout-buttons {
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
  }

  .toggle-controls {
    flex-direction: row;
    gap: 8px;
  }

  .preview-container .preview-header {
    padding: 12px;

    h3 {
      font-size: 16px;
    }
  }
}

@media (max-width: 480px) {
  .controls-panel {
    padding: 8px;
    max-height: 200px;

    .controls-header {
      margin-bottom: 16px;

      h1 {
        font-size: 20px;
      }

      p {
        font-size: 12px;
      }
    }

    .controls-section {
      margin-bottom: 16px;

      h3 {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
  }

  .layout-buttons {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
  }

  .layout-btn {
    padding: 6px 8px;
    font-size: 10px;
  }

  .toggle-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .preview-container .preview-header {
    padding: 8px;
    flex-direction: column;
    gap: 8px;

    h3 {
      font-size: 14px;
    }

    .preview-info {
      gap: 4px;
    }

    .info-badge {
      padding: 2px 6px;
      font-size: 10px;
    }
  }
}
