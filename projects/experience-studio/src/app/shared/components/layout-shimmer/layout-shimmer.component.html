<div class="layout-shimmer" 
     [ngClass]="{
       'theme-light': theme === 'light',
       'theme-dark': theme === 'dark',
       'animated': animated
     }">
  
  <!-- Header -->
  <div *ngIf="layoutConfig.hasHeader" class="header">
    <div class="header-content">
      <div class="header-left">
        <div class="shimmer-element logo"></div>
        <div class="shimmer-element nav-item"></div>
        <div class="shimmer-element nav-item"></div>
      </div>
      <div class="header-right">
        <div class="shimmer-element avatar"></div>
        <div class="shimmer-element button"></div>
      </div>
    </div>
  </div>

  <div class="main-container">
    <!-- Left Sidebar -->
    <div *ngIf="layoutConfig.hasLeftSidebar" class="sidebar left-sidebar">
      <div class="sidebar-content">
        <div class="shimmer-element sidebar-title"></div>
        <div class="sidebar-section">
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
        </div>
        <div class="shimmer-element sidebar-title"></div>
        <div class="sidebar-section">
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
        </div>
      </div>
    </div>

    <!-- Body -->
    <div class="body">
      <div class="body-content">
        <div class="shimmer-element page-title"></div>
        
        <div class="content-grid">
          <div class="content-card">
            <div class="shimmer-element card-image"></div>
            <div class="shimmer-element card-title"></div>
            <div class="shimmer-element card-text"></div>
          </div>
          <div class="content-card">
            <div class="shimmer-element card-image"></div>
            <div class="shimmer-element card-title"></div>
            <div class="shimmer-element card-text"></div>
          </div>
          <div class="content-card">
            <div class="shimmer-element card-image"></div>
            <div class="shimmer-element card-title"></div>
            <div class="shimmer-element card-text"></div>
          </div>
        </div>

        <div class="text-content">
          <div class="shimmer-element text-line full"></div>
          <div class="shimmer-element text-line three-quarter"></div>
          <div class="shimmer-element text-line full"></div>
          <div class="shimmer-element text-line half"></div>
        </div>
      </div>
    </div>

    <!-- Right Sidebar -->
    <div *ngIf="layoutConfig.hasRightSidebar" class="sidebar right-sidebar">
      <div class="sidebar-content">
        <div class="shimmer-element sidebar-title"></div>
        <div class="sidebar-section">
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
        </div>
        <div class="shimmer-element sidebar-title"></div>
        <div class="sidebar-section">
          <div class="shimmer-element sidebar-item"></div>
          <div class="shimmer-element sidebar-item"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div *ngIf="layoutConfig.hasFooter" class="footer">
    <div class="footer-content">
      <div class="footer-section">
        <div class="shimmer-element footer-title"></div>
        <div class="shimmer-element footer-link"></div>
        <div class="shimmer-element footer-link"></div>
      </div>
      <div class="footer-section">
        <div class="shimmer-element footer-title"></div>
        <div class="shimmer-element footer-link"></div>
        <div class="shimmer-element footer-link"></div>
      </div>
      <div class="footer-section">
        <div class="shimmer-element footer-title"></div>
        <div class="shimmer-element footer-link"></div>
        <div class="shimmer-element footer-link"></div>
      </div>
      <div class="footer-section">
        <div class="shimmer-element footer-title"></div>
        <div class="shimmer-element footer-link"></div>
        <div class="shimmer-element footer-link"></div>
      </div>
    </div>
  </div>
</div>
