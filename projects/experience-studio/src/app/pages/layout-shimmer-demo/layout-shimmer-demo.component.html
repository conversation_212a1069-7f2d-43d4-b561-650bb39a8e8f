<div class="demo-container" [ngClass]="'theme-' + selectedTheme">
  <!-- Controls -->
  <div class="controls-panel">
    <div class="controls-header">
      <h1>Layout Shimmer Demo</h1>
      <p>Interactive demonstration of animated layout shimmer components</p>
    </div>

    <div class="controls-section">
      <h3>Layout Selection</h3>
      <div class="layout-buttons">
        <button
          *ngFor="let layout of layouts"
          class="layout-btn"
          [class.active]="selectedLayout === layout.key"
          (click)="selectLayout(layout.key)">
          {{ layout.key }}
        </button>
      </div>
      <p class="layout-description">
        {{ selectedLayoutName }}
      </p>
    </div>

    <div class="controls-section">
      <h3>Theme & Animation</h3>
      <div class="toggle-controls">
        <button class="toggle-btn" (click)="toggleTheme()">
          <span class="toggle-icon">🌓</span>
          {{ selectedTheme === 'light' ? 'Switch to Dark' : 'Switch to Light' }}
        </button>
        <button class="toggle-btn" (click)="toggleAnimation()">
          <span class="toggle-icon">{{ isAnimated ? '⏸️' : '▶️' }}</span>
          {{ isAnimated ? 'Disable Animation' : 'Enable Animation' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Preview -->
  <div class="preview-container">
    <div class="preview-header">
      <h3>Preview: {{ selectedLayout }}</h3>
      <div class="preview-info">
        <span class="info-badge theme-badge">{{ selectedTheme }}</span>
        <span class="info-badge animation-badge" [class.active]="isAnimated">
          {{ isAnimated ? 'Animated' : 'Static' }}
        </span>
      </div>
    </div>

    <div class="preview-frame">
      <app-layout-shimmer
        [layoutKey]="selectedLayout"
        [theme]="selectedTheme"
        [animated]="isAnimated">
      </app-layout-shimmer>
    </div>
  </div>
</div>
