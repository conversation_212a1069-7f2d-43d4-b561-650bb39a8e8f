import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface LayoutConfig {
  key: string;
  name: string;
  hasHeader: boolean;
  hasLeftSidebar: boolean;
  hasRightSidebar: boolean;
  hasFooter: boolean;
}

@Component({
  selector: 'app-layout-shimmer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './layout-shimmer.component.html',
  styleUrls: ['./layout-shimmer.component.scss']
})
export class LayoutShimmerComponent implements OnInit, OnChanges {
  @Input() layoutKey: string = 'HB';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() animated: boolean = true;

  layoutConfig: LayoutConfig = {
    key: 'HB',
    name: 'Header + Body (No Sidebars, No Footer)',
    hasHeader: true,
    hasLeftSidebar: false,
    hasRightSidebar: false,
    hasFooter: false
  };

  private layoutMapping: { [key: string]: LayoutConfig } = {
    HB: {
      key: 'HB',
      name: 'Header + Body (No Sidebars, No Footer)',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: false,
      hasFooter: false
    },
    HBF: {
      key: 'HBF',
      name: 'Header + Body + Footer (No Sidebars)',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: false,
      hasFooter: true
    },
    HLSB: {
      key: 'HLSB',
      name: 'Header + Left Sidebar + Body (No Footer)',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: false,
      hasFooter: false
    },
    HLSBF: {
      key: 'HLSBF',
      name: 'Header + Left Sidebar + Body + Footer',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: false,
      hasFooter: true
    },
    HBRS: {
      key: 'HBRS',
      name: 'Header + Body + Right Sidebar (No Footer)',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: true,
      hasFooter: false
    },
    HBRSF: {
      key: 'HBRSF',
      name: 'Header + Body + Right Sidebar + Footer',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: true,
      hasFooter: true
    },
    HLSBRS: {
      key: 'HLSBRS',
      name: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: true,
      hasFooter: false
    },
    HLSBRSF: {
      key: 'HLSBRSF',
      name: 'Header + Left Sidebar + Body + Right Sidebar + Footer',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: true,
      hasFooter: true
    }
  };

  ngOnInit(): void {
    this.updateLayoutConfig();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['layoutKey']) {
      this.updateLayoutConfig();
    }
  }

  private updateLayoutConfig(): void {
    this.layoutConfig = this.layoutMapping[this.layoutKey] || this.layoutMapping['HB'];
  }
}
