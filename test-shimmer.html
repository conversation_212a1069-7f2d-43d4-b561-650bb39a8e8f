<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Shimmer Test</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; background: #f8f9fa; }
        
        .shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .layout { min-height: 100vh; display: flex; flex-direction: column; }
        .header { background: white; border-bottom: 1px solid #e5e7eb; padding: 16px 24px; display: flex; justify-content: space-between; align-items: center; }
        .sidebar { width: 250px; background: #f9fafb; border-right: 1px solid #e5e7eb; padding: 24px; }
        .sidebar.right { border-right: none; border-left: 1px solid #e5e7eb; }
        .main { flex: 1; display: flex; }
        .body { flex: 1; padding: 24px; background: white; }
        .footer { background: #111827; color: white; padding: 32px 24px; }
        
        .h-4 { height: 16px; }
        .h-6 { height: 24px; }
        .h-8 { height: 32px; }
        .h-12 { height: 48px; }
        .h-32 { height: 128px; }
        
        .w-16 { width: 64px; }
        .w-20 { width: 80px; }
        .w-24 { width: 96px; }
        .w-32 { width: 128px; }
        .w-1\/2 { width: 50%; }
        .w-3\/4 { width: 75%; }
        .w-full { width: 100%; }
        
        .rounded { border-radius: 6px; }
        .rounded-full { border-radius: 50%; }
        .mb-2 { margin-bottom: 8px; }
        .mb-4 { margin-bottom: 16px; }
        .mb-6 { margin-bottom: 24px; }
        .mr-4 { margin-right: 16px; }
        .space-y-2 > * + * { margin-top: 8px; }
        .space-y-4 > * + * { margin-top: 16px; }
        .space-x-4 > * + * { margin-left: 16px; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; }
        
        .selector { background: white; border-bottom: 1px solid #e5e7eb; padding: 16px 24px; }
        .btn { padding: 8px 16px; margin: 4px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer; font-size: 12px; }
        .btn.active { background: #dbeafe; border-color: #3b82f6; color: #1e40af; }
        .btn:hover { background: #f9fafb; }
        
        @media (max-width: 768px) {
            .sidebar { display: none; }
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="selector">
        <div style="display: flex; flex-wrap: wrap;">
            <button class="btn active" onclick="showLayout('HB')">HB</button>
            <button class="btn" onclick="showLayout('HBF')">HBF</button>
            <button class="btn" onclick="showLayout('HLSB')">HLSB</button>
            <button class="btn" onclick="showLayout('HLSBF')">HLSBF</button>
            <button class="btn" onclick="showLayout('HBRS')">HBRS</button>
            <button class="btn" onclick="showLayout('HBRSF')">HBRSF</button>
            <button class="btn" onclick="showLayout('HLSBRS')">HLSBRS</button>
            <button class="btn" onclick="showLayout('HLSBRSF')">HLSBRSF</button>
        </div>
    </div>

    <div id="layout" class="layout">
        <!-- Header -->
        <div class="header">
            <div class="flex items-center space-x-4">
                <div class="shimmer w-32 h-8 rounded"></div>
                <div class="shimmer w-20 h-4 rounded"></div>
                <div class="shimmer w-24 h-4 rounded"></div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="shimmer w-8 h-8 rounded-full"></div>
                <div class="shimmer w-20 h-8 rounded"></div>
            </div>
        </div>

        <div class="main">
            <!-- Left Sidebar -->
            <div id="leftSidebar" class="sidebar" style="display: none;">
                <div class="space-y-4">
                    <div class="shimmer w-32 h-6 rounded mb-2"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-24 h-4 rounded"></div>
                        <div class="shimmer w-20 h-4 rounded"></div>
                        <div class="shimmer w-28 h-4 rounded"></div>
                    </div>
                    <div class="shimmer w-28 h-6 rounded mb-2"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-32 h-4 rounded"></div>
                        <div class="shimmer w-16 h-4 rounded"></div>
                    </div>
                </div>
            </div>

            <!-- Body -->
            <div class="body">
                <div class="space-y-4">
                    <div class="shimmer w-1/2 h-8 rounded mb-6"></div>
                    
                    <div class="grid">
                        <div class="space-y-4">
                            <div class="shimmer w-full h-32 rounded"></div>
                            <div class="shimmer w-3/4 h-4 rounded"></div>
                            <div class="shimmer w-1/2 h-4 rounded"></div>
                        </div>
                        <div class="space-y-4">
                            <div class="shimmer w-full h-32 rounded"></div>
                            <div class="shimmer w-3/4 h-4 rounded"></div>
                            <div class="shimmer w-1/2 h-4 rounded"></div>
                        </div>
                        <div class="space-y-4">
                            <div class="shimmer w-full h-32 rounded"></div>
                            <div class="shimmer w-3/4 h-4 rounded"></div>
                            <div class="shimmer w-1/2 h-4 rounded"></div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="shimmer w-full h-4 rounded"></div>
                        <div class="shimmer w-3/4 h-4 rounded"></div>
                        <div class="shimmer w-full h-4 rounded"></div>
                        <div class="shimmer w-1/2 h-4 rounded"></div>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar -->
            <div id="rightSidebar" class="sidebar right" style="display: none;">
                <div class="space-y-4">
                    <div class="shimmer w-32 h-6 rounded mb-2"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-24 h-4 rounded"></div>
                        <div class="shimmer w-20 h-4 rounded"></div>
                        <div class="shimmer w-28 h-4 rounded"></div>
                    </div>
                    <div class="shimmer w-28 h-6 rounded mb-2"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-32 h-4 rounded"></div>
                        <div class="shimmer w-16 h-4 rounded"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div id="footer" class="footer" style="display: none;">
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 24px;">
                <div class="space-y-4">
                    <div class="shimmer w-24 h-6 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-20 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                        <div class="shimmer w-16 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="shimmer w-24 h-6 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-20 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                        <div class="shimmer w-16 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="shimmer w-24 h-6 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-20 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                        <div class="shimmer w-16 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="shimmer w-24 h-6 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    <div class="space-y-2">
                        <div class="shimmer w-20 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                        <div class="shimmer w-16 h-4 rounded" style="background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%); background-size: 200% 100%; animation: shimmer 1.5s infinite;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showLayout(type) {
            // Reset active button
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Hide all components
            document.getElementById('leftSidebar').style.display = 'none';
            document.getElementById('rightSidebar').style.display = 'none';
            document.getElementById('footer').style.display = 'none';
            
            // Show based on type
            if (type.includes('LS')) document.getElementById('leftSidebar').style.display = 'block';
            if (type.includes('RS')) document.getElementById('rightSidebar').style.display = 'block';
            if (type.includes('F')) document.getElementById('footer').style.display = 'block';
        }
    </script>
</body>
</html>
