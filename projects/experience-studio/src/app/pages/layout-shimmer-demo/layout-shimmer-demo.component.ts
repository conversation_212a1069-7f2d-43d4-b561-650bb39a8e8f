import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutShimmerComponent } from '../../shared/components/layout-shimmer/layout-shimmer.component';

@Component({
  selector: 'app-layout-shimmer-demo',
  standalone: true,
  imports: [CommonModule, LayoutShimmerComponent],
  templateUrl: './layout-shimmer-demo.component.html',
  styleUrls: ['./layout-shimmer-demo.component.scss']
})
export class LayoutShimmerDemoComponent {
  selectedLayout: string = 'HB';
  selectedTheme: 'light' | 'dark' = 'light';
  isAnimated: boolean = true;

  layouts = [
    { key: 'HB', name: 'Header + Body (No Sidebars, No Footer)' },
    { key: 'HBF', name: 'Header + Body + Footer (No Sidebars)' },
    { key: 'HLSB', name: 'Header + Left Sidebar + Body (No Footer)' },
    { key: 'HLSBF', name: 'Header + Left Sidebar + Body + Footer' },
    { key: 'HBRS', name: 'Header + Body + Right Sidebar (No Footer)' },
    { key: 'HBRSF', name: 'Header + Body + Right Sidebar + Footer' },
    { key: 'HLSBRS', name: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)' },
    { key: 'HLSBRSF', name: 'Header + Left Sidebar + Body + Right Sidebar + Footer' }
  ];

  selectLayout(layoutKey: string): void {
    this.selectedLayout = layoutKey;
  }

  toggleTheme(): void {
    this.selectedTheme = this.selectedTheme === 'light' ? 'dark' : 'light';
  }

  toggleAnimation(): void {
    this.isAnimated = !this.isAnimated;
  }

  get selectedLayoutName(): string {
    return this.layouts.find(l => l.key === this.selectedLayout)?.name || '';
  }
}
